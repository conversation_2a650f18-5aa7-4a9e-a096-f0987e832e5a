export interface GooglePayConfig {
  enabled?: boolean;
  merchantName?: string;
  environment?: "TEST" | "PRODUCTION";
  allowedCardNetworks?: Array<"VISA" | "MASTERCARD" | "AMEX" | "DISCOVER" | "JCB" | "INTERAC">;
  allowedCardAuthMethods?: Array<"PAN_ONLY" | "CRYPTOGRAM_3DS">;
  billingAddressRequired?: boolean;
  shippingAddressRequired?: boolean;
  phoneNumberRequired?: boolean;
}

export interface IntegrationTokenRequest {
  merchantId: string;
  description: string;
  amount?: number;
  returnUrl?: string;
  expiresIn?: number;
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  googlePayConfig?: GooglePayConfig;
  enableDigitalWallets?: boolean;
}

export interface IntegrationTokenResponse {
  token: string;
  expiresAt: string;
  embedUrl: string;
  merchantInfo: {
    id: string;
    name: string;
    status: number;
  };
}

export interface TokenValidationResult {
  isValid: boolean;
  data?: {
    merchantId: string;
    description: string;
    amount: number;
    returnUrl?: string;
    currency?: string;
    items?: Array<{
      name: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      total: number;
      commodityCode?: string;
      productCode?: string;
    }>;
    taxAmount?: number;
    shippingAmount?: number;
    dutyAmount?: number;
    orderNumber?: string;
    invoiceNumber?: string;
    customerCode?: string;
    orderDiscount?: number;
    googlePayConfig?: GooglePayConfig;
    enableDigitalWallets?: boolean;
    merchantInfo?: {
      address?: {
        line1?: string;
        line2?: string;
        city?: string;
        state?: string;
        zip?: string;
        country?: string;
      };
      contactEmail?: string;
      contactPhone?: string;
    };
  };
  error?: string;
}
