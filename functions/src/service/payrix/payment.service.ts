import { AxiosError } from "axios";
import { PaymentResult, PayrixError, TokenDeletionResult, TokenPaymentData } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";

const apiClient = createPayrixApiClient();

export async function processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult> {
  try {
    const transactionData = {
      merchant: paymentData.merchantId,
      type: "1",
      origin: "2",
      token: paymentData.token,
      total: paymentData.amount.toString(),
      description: paymentData.description || "Token-based payment",
      ...(paymentData.customerInfo?.email && { email: paymentData.customerInfo.email }),
      ...(paymentData.customerInfo?.name && { name: paymentData.customerInfo.name }),
      ...(paymentData.customerInfo?.address && {
        address1: paymentData.customerInfo.address.line1,
        address2: paymentData.customerInfo.address.line2,
        city: paymentData.customerInfo.address.city,
        state: paymentData.customerInfo.address.state,
        zip: paymentData.customerInfo.address.zip,
        country: paymentData.customerInfo.address.country,
      }),
    };

    const response = await apiClient.post("/txns", transactionData);

    let transactionData_response;

    if (response.data?.response?.data?.[0]) {
      transactionData_response = response.data.response.data[0];
    } else if (response.data?.data?.[0]) {
      transactionData_response = response.data.data[0];
    } else if (response.data?.id) {
      transactionData_response = response.data;
    }

    const errors = response.data?.response?.errors || response.data?.errors;
    if (errors && Array.isArray(errors) && errors.length > 0) {
      const errorMessages = errors.map((err: PayrixError) => `${err.field || "general"}: ${err.msg}`).join(", ");
      throw new Error(`Payrix API errors: ${errorMessages}`);
    }

    if (!transactionData_response) {
      throw new Error("Invalid Payrix response structure: no transaction data found");
    }

    return {
      success: true,
      transaction: transactionData_response,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorData = axiosError.response?.data as { message?: string } | undefined;

    return {
      success: false,
      error: `Payment processing failed: ${errorData?.message || axiosError.message}`,
    };
  }
}

export async function cleanupToken(tokenId: string): Promise<TokenDeletionResult> {
  try {
    // Use the tokenId (format: t1_tok_*) for the DELETE /tokens/{id} API call
    await apiClient.delete(`/tokens/${tokenId}`);

    return {
      success: true,
      message: "Token deleted successfully",
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    return {
      success: false,
      error: `Token cleanup failed: ${axiosError.message}`,
    };
  }
}
