import { postMessageToParent } from "./iframe-communication";

export interface ScriptLoaderOptions {
  onLoad?: () => void;
  onError?: (error: Error | Event) => void;
}

export const loadPayFieldsScript = (options: ScriptLoaderOptions = {}): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.PayFields) {
      resolve();
      return;
    }

    // Load PayFields script directly (Google Pay is handled by PayFields)
    const script = document.createElement("script");
    script.src = `${import.meta.env.VITE_PAYRIX_PAYMENT_URL || "https://test-api.payrix.com/payFieldsScript"}?spa=1&iframe=1`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      postMessageToParent("PAYFIELDS_SCRIPT_LOADED");

      if (options.onLoad) options.onLoad();
      resolve();
    };

    script.onerror = (error) => {
      const errorMessage = "Failed to load payment processing script. Please try again later.";

      postMessageToParent("PAYFIELDS_SCRIPT_ERROR", { error: errorMessage });

      const err = new Error(errorMessage);
      if (options.onError) options.onError(error instanceof Event ? error : new Error(String(error)));
      reject(err);
    };

    document.body.appendChild(script);
  });
};

export const cleanupPayFieldsScript = (): void => {
  const scripts = document.querySelectorAll('script[src*="payFieldsScript"]');
  scripts.forEach((script) => {
    if (document.body.contains(script)) {
      document.body.removeChild(script);
    }
  });

  if (window.PayFields && typeof window.PayFields.unmountAll === "function") {
    window.PayFields.unmountAll();
  }
};
