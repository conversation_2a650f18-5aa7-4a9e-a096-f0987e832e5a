interface PaymentMethodSelectorProps {
  onMethodSelect: (method: "card" | "google_pay") => void;
  selectedMethod?: "card" | "google_pay";
  disabled?: boolean;
}

export const PaymentMethodSelector = ({
  onMethodSelect,
  selectedMethod,
  disabled = false,
}: PaymentMethodSelectorProps) => {
  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium text-gray-700 mb-3">Select Payment Method</h3>
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={() => onMethodSelect("card")}
          disabled={disabled}
          className={`
            relative p-4 border-2 rounded-lg transition-all duration-200
            ${
              selectedMethod === "card"
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300 bg-white"
            }
            ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
          `}
        >
          <div className="flex flex-col items-center">
            <svg
              className={`w-8 h-8 mb-2 ${
                selectedMethod === "card" ? "text-blue-600" : "text-gray-600"
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
              />
            </svg>
            <span className={`text-sm font-medium ${
              selectedMethod === "card" ? "text-blue-700" : "text-gray-700"
            }`}>
              Pay with Card
            </span>
            <span className="text-xs text-gray-500 mt-1">Save for future use</span>
          </div>
          {selectedMethod === "card" && (
            <div className="absolute top-2 right-2">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          )}
        </button>

        <button
          onClick={() => onMethodSelect("google_pay")}
          disabled={disabled}
          className={`
            relative p-4 border-2 rounded-lg transition-all duration-200
            ${
              selectedMethod === "google_pay"
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300 bg-white"
            }
            ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
          `}
        >
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 mb-2 flex items-center justify-center">
              <svg
                viewBox="0 0 435.97 173.13"
                className="w-full h-full"
              >
                <g>
                  <path
                    fill="#4285f4"
                    d="M207.66,84.35v48.48h-16.15V20.47h42.73a34.41,34.41,0,0,1,14.39,3,37.66,37.66,0,0,1,11.69,8,37.27,37.27,0,0,1,11,27.59,37.82,37.82,0,0,1-11,27.73A37.66,37.66,0,0,1,248.6,95a34.41,34.41,0,0,1-14.39,3H207.66Zm0-48.62V68.47h26.71a18.41,18.41,0,0,0,13.65-5.52,19.53,19.53,0,0,0,0-27.42,18.77,18.77,0,0,0-13.65-5.39H207.66Z"
                  />
                  <path
                    fill="#34a853"
                    d="M303.88,53a34.16,34.16,0,0,1,25.26,9.59q9.53,9.59,9.52,26v44.24H324.14v-9.92h-.57a24.86,24.86,0,0,1-9.29,8.23,28.32,28.32,0,0,1-13.36,3.14q-11.28,0-18.46-6.5t-7.19-16.42a20.89,20.89,0,0,1,7.61-16.85q7.62-6.36,22.43-6.36a60.16,60.16,0,0,1,18,2.42v-1.7a14.13,14.13,0,0,0-5.1-11.12,17.63,17.63,0,0,0-11.89-4.39,18.54,18.54,0,0,0-17.28,10.49l-14.37-9a28.89,28.89,0,0,1,11.94-13.36A38.15,38.15,0,0,1,303.88,53Zm-14,57.7q0,5.4,4,8.52a14.72,14.72,0,0,0,9.59,3.13,22.85,22.85,0,0,0,14.93-5.73q6.5-5.73,6.5-13.34-5.55-2.42-14.65-2.41-7.83,0-12.45,2.76T289.84,110.68Z"
                  />
                  <path
                    fill="#fbbc04"
                    d="M410.57,54.67L369.12,153.52H352.26L368.13,116,340,54.67h17.56L376,115.13h.56L393.85,54.67Z"
                  />
                  <path
                    fill="#4285f4"
                    d="M159.75,69.61a45.66,45.66,0,0,0-.67-8H81.77v18.85h44.37A19.38,19.38,0,0,1,117.55,94h0l-14.41,11.18c5.61,5.5,13.31,8.91,22.63,8.91A31.38,31.38,0,0,0,159.75,84.6,27.8,27.8,0,0,0,159.75,69.61Z"
                  />
                  <path
                    fill="#34a853"
                    d="M68.42,78.77A32,32,0,0,0,66.84,88a31.8,31.8,0,0,0,1.6,9.27v0L82.91,86.14h0a18.86,18.86,0,0,1-1.14-5.69,19.13,19.13,0,0,1,6.78-14.54h0L74.09,54.73A31.55,31.55,0,0,0,68.42,78.77Z"
                  />
                  <path
                    fill="#fbbc04"
                    d="M81.77,61.61a19.51,19.51,0,0,1,13.76,5.39h0l14.41-14.41A34.47,34.47,0,0,0,81.77,42.17a31.73,31.73,0,0,0-7.68,12.56h0L88.55,65.91A18.94,18.94,0,0,1,81.77,61.61Z"
                  />
                  <path
                    fill="#ea4335"
                    d="M81.77,133.82A31.57,31.57,0,0,0,107.33,121h0L92.86,109.86a19,19,0,0,1-11.09,3.51,19.14,19.14,0,0,1-18.1-13.74h0L49.2,110.81A31.75,31.75,0,0,0,81.77,133.82Z"
                  />
                </g>
              </svg>
            </div>
            <span className={`text-sm font-medium ${
              selectedMethod === "google_pay" ? "text-blue-700" : "text-gray-700"
            }`}>
              Google Pay
            </span>
            <span className="text-xs text-gray-500 mt-1">Fast & secure</span>
          </div>
          {selectedMethod === "google_pay" && (
            <div className="absolute top-2 right-2">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          )}
        </button>
      </div>
    </div>
  );
};