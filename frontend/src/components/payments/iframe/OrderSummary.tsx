import type { MerchantInfo as MerchantInfoType, PaymentInfo } from "../../../types/payment";
import { OrderOverview } from "./OrderOverview";
import { MerchantInfo } from "./MerchantInfo";
import { PoweredBySection } from "./PoweredBySection";

interface OrderSummaryProps {
  paymentInfo: PaymentInfo | null;
  merchantInfo: MerchantInfoType | null;
}

export const OrderSummary = ({ paymentInfo, merchantInfo }: OrderSummaryProps) => {
  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Order Summary</h2>

        {/* Order Overview */}
        {paymentInfo && <OrderOverview paymentInfo={paymentInfo} />}

        {/* Merchant Information - Progressive disclosure */}
        {merchantInfo && (
          <div className="mt-3">
            <MerchantInfo merchantInfo={merchantInfo} />
          </div>
        )}
      </div>

      {/* Powered by section - Minimized */}
      <PoweredBySection />
    </div>
  );
};
