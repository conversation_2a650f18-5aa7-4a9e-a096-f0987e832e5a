import PaymentLogos from "../PaymentLogos";
import SecurePayFields from "../SecurePayFields";
import { PaymentMethodSelector } from "../PaymentMethodSelector";
import type { PayFieldsConfig, BillingAddress, PaymentInfo } from "../../../types/payment";
import { PaymentValidation } from "./PaymentValidation";

interface PaymentFormSectionProps {
  payFieldsConfig: PayFieldsConfig | null;
  paymentInfo: PaymentInfo | null;
  billingAddress: BillingAddress;
  error: string | null;
  isAddressValid: boolean;
  loading?: boolean;
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
  selectedPaymentMethod: "card" | "google_pay" | null;
  onPaymentMethodSelect: (method: "card" | "google_pay") => void;
}

export const PaymentFormSection = ({
  payFieldsConfig,
  paymentInfo,
  billingAddress,
  error,
  isAddressValid,
  loading = false,
  onSuccess,
  onFailure,
  selectedPaymentMethod,
  onPaymentMethodSelect,
}: PaymentFormSectionProps) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Information</h2>

      {/* Payment method selector */}
      {!selectedPaymentMethod && (
        <div className="mb-6">
          <PaymentMethodSelector
            onMethodSelect={onPaymentMethodSelect}
            selectedMethod={selectedPaymentMethod || undefined}
            disabled={!isAddressValid}
          />
        </div>
      )}

      {/* Show content only after payment method is selected */}
      {selectedPaymentMethod && (
        <>
          {/* Change payment method button */}
          <div className="mb-4">
            <button
              onClick={() => onPaymentMethodSelect(selectedPaymentMethod === "card" ? "google_pay" : "card")}
              className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
              Switch to {selectedPaymentMethod === "card" ? "Google Pay" : "Card Payment"}
            </button>
          </div>

          {/* Accepted cards */}
          <div className="mb-4 flex flex-col">
            <p className="text-sm text-gray-600 mb-2">Accepted Payment Methods</p>
            <PaymentLogos showGooglePay={payFieldsConfig?.googlePayConfig?.enabled || false} />
          </div>

          <div className="mb-4">
            <PaymentValidation error={error} isAddressValid={isAddressValid} />
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
              <p className="mt-4 text-gray-600">Switching payment method...</p>
            </div>
          ) : payFieldsConfig ? (
            <div className={!isAddressValid ? "opacity-50 pointer-events-none" : ""}>
              <SecurePayFields
                key={`${selectedPaymentMethod}-${payFieldsConfig.googlePayConfig?.enabled}`}
                config={payFieldsConfig}
                paymentInfo={paymentInfo}
                onSuccess={onSuccess}
                onFailure={onFailure}
                billingAddress={billingAddress}
                paymentMethod={selectedPaymentMethod}
              />
            </div>
          ) : null}

          {/* Save card option - only show for card payments */}
          {selectedPaymentMethod === "card" && (
            <div className="mt-4">
              <label className="flex items-center text-sm text-gray-600">
                <input type="checkbox" className="mr-2 h-4 w-4 text-[#364F6B] border-gray-300 rounded focus:ring-[#364F6B]" />
                Save card for future payments
              </label>
            </div>
          )}

          {/* Payment note */}
          <div className="mt-4 text-xs text-gray-500 text-center">
            <p>Payment processed securely. Fill in the form and click &quot;Pay Now&quot; to proceed.</p>
          </div>
        </>
      )}
    </div>
  );
};
