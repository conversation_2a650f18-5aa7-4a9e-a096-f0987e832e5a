import { useEffect, useState, useCallback, useRef } from "react";
import { GooglePayConfig } from "./types/payfields.types";
import { toast } from "sonner";

interface GooglePayButtonProps {
  config: GooglePayConfig;
  merchantId: string;
  amount: number;
  currency?: string;
  onPaymentAuthorized: (paymentData: google.payments.api.PaymentData) => void;
  onError?: (error: Error) => void;
  className?: string;
}

declare global {
  interface Window {
    google?: {
      payments: {
        api: {
          PaymentsClient: new (config: { environment: string }) => google.payments.api.PaymentsClient;
        };
      };
    };
  }
}

export const GooglePayButton = ({
  config,
  merchantId,
  amount,
  currency = "USD",
  onPaymentAuthorized,
  onError,
  className = "",
}: GooglePayButtonProps) => {
  const [paymentsClient, setPaymentsClient] = useState<google.payments.api.PaymentsClient | null>(null);
  const [isReadyToPay, setIsReadyToPay] = useState(false);
  const [buttonContainer, setButtonContainer] = useState<HTMLDivElement | null>(null);
  const initializingRef = useRef(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const baseRequest = {
    apiVersion: 2,
    apiVersionMinor: 0,
  };

  const tokenizationSpecification: google.payments.api.PaymentMethodTokenizationSpecification = {
    type: "PAYMENT_GATEWAY",
    parameters: {
      gateway: "payrix",
      gatewayMerchantId: merchantId,
    },
  };

  const cardPaymentMethod: google.payments.api.CardParameters = {
    allowedAuthMethods: config.allowedCardAuthMethods || ["PAN_ONLY", "CRYPTOGRAM_3DS"],
    allowedCardNetworks: config.allowedCardNetworks || ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
    billingAddressRequired: config.billingAddressRequired ?? true,
    billingAddressParameters: {
      format: "FULL",
      phoneNumberRequired: config.phoneNumberRequired ?? false,
    },
  };

  const loadGooglePayScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      // Check if script is already loaded
      if (window.google?.payments?.api) {
        resolve();
        return;
      }

      // Check if script tag already exists (from HTML head)
      const existingScript = document.querySelector('script[src*="pay.google.com"]');
      if (existingScript) {
        // Clear any existing interval
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }

        // Wait for existing script to load
        intervalRef.current = setInterval(() => {
          if (window.google?.payments?.api) {
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            resolve();
          }
        }, 100);

        // Timeout after 5 seconds
        setTimeout(() => {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          if (!window.google?.payments?.api) {
            reject(new Error("Google Pay script loading timeout"));
          }
        }, 5000);
        return;
      }

      // Fallback: dynamically load script if not present
      const script = document.createElement("script");
      script.src = "https://pay.google.com/gp/p/js/pay.js";
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error("Failed to load Google Pay script"));
      document.head.appendChild(script);
    });
  }, []);

  const initializeGooglePay = useCallback(async () => {
    // Prevent multiple simultaneous initializations
    if (initializingRef.current || paymentsClient) {
      return;
    }

    initializingRef.current = true;

    try {
      await loadGooglePayScript();

      if (!window.google?.payments?.api) {
        throw new Error("Google Pay API not available");
      }

      const client = new window.google.payments.api.PaymentsClient({
        environment: config.environment || "TEST",
      });

      setPaymentsClient(client);

      const isReadyToPayRequest: google.payments.api.IsReadyToPayRequest = {
        ...baseRequest,
        allowedPaymentMethods: [
          {
            type: "CARD",
            parameters: cardPaymentMethod,
            tokenizationSpecification,
          },
        ],
      };

      const response = await client.isReadyToPay(isReadyToPayRequest);
      setIsReadyToPay(response.result || false);
    } catch (error) {
      console.error("Error initializing Google Pay:", error);
      setIsReadyToPay(false);
      if (onError) {
        onError(error instanceof Error ? error : new Error("Failed to initialize Google Pay"));
      }
    } finally {
      initializingRef.current = false;
    }
  }, [config.environment, cardPaymentMethod, tokenizationSpecification, loadGooglePayScript, onError, paymentsClient]);

  const onGooglePaymentButtonClicked = useCallback(async () => {
    if (!paymentsClient) {
      toast.error("Google Pay not initialized");
      return;
    }

    const paymentDataRequest: google.payments.api.PaymentDataRequest = {
      ...baseRequest,
      allowedPaymentMethods: [
        {
          type: "CARD",
          parameters: cardPaymentMethod,
          tokenizationSpecification,
        },
      ],
      merchantInfo: {
        merchantName: config.merchantName || "Auth-Clear",
        merchantId: config.environment === "PRODUCTION" ? merchantId : undefined,
      },
      transactionInfo: {
        totalPriceStatus: "FINAL",
        totalPrice: (amount / 100).toFixed(2),
        currencyCode: currency,
        countryCode: "US",
      },
    };

    if (config.shippingAddressRequired) {
      paymentDataRequest.shippingAddressRequired = true;
      paymentDataRequest.shippingAddressParameters = {
        phoneNumberRequired: config.phoneNumberRequired ?? false,
      };
    }

    console.log("merchantId to create token with google pay", merchantId);

    try {
      const paymentData = await paymentsClient.loadPaymentData(paymentDataRequest);
      onPaymentAuthorized(paymentData);
    } catch (error) {
      if ((error as { statusCode?: string }).statusCode === "CANCELED") {
        toast.info("Payment cancelled");
      } else {
        console.error("Error processing Google Pay:", error);
        toast.error("Failed to process Google Pay payment");
        if (onError) {
          onError(error instanceof Error ? error : new Error("Payment processing failed"));
        }
      }
    }
  }, [paymentsClient, amount, currency, merchantId, config, cardPaymentMethod, tokenizationSpecification, onPaymentAuthorized, onError]);

  useEffect(() => {
    // Only initialize if enabled and not already initialized
    if (config.enabled !== false && !paymentsClient && !initializingRef.current) {
      initializeGooglePay();
    }
  }, [config.enabled, initializeGooglePay]); // Removed isReadyToPay to prevent re-initialization loop

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      initializingRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (isReadyToPay && paymentsClient && buttonContainer) {
      const button = paymentsClient.createButton({
        onClick: onGooglePaymentButtonClicked,
        buttonType: "short",
        buttonColor: "default",
        buttonSizeMode: "fill",
      });

      buttonContainer.innerHTML = "";
      buttonContainer.appendChild(button);
    }
  }, [isReadyToPay, paymentsClient, buttonContainer, onGooglePaymentButtonClicked]);

  if (config.enabled === false || !isReadyToPay) {
    return null;
  }

  return (
    <div className={`google-pay-button-container ${className}`}>
      <div ref={setButtonContainer} className="w-full" />
    </div>
  );
};
