import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "sonner";
import { PayFieldsConfig, BillingAddress } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import { loadPayFieldsScript } from "../utils/script-loader";
import { postMessageToParent } from "../utils/iframe-communication";
import { usePayFieldsErrors } from "./usePayFieldsErrors";
import { usePayFieldsInitializer } from "./usePayFieldsInitializer";

interface UsePayFieldsOptions {
  config: PayFieldsConfig;
  paymentInfo?: PaymentInfo | null;
  billingAddress?: BillingAddress;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
}

interface UsePayFieldsReturn {
  loaded: boolean;
  scriptError: string | null;
  isSubmitting: boolean;
  validationError: string | null;
  handleSubmit: () => void;
}

export const usePayFields = ({ config, paymentInfo, billingAddress, onSuccess, onFailure }: UsePayFieldsOptions): UsePayFieldsReturn => {
  const [loaded, setLoaded] = useState(false);
  const [scriptError, setScriptError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [payFieldsInitialized, setPayFieldsInitialized] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const { handlePaymentSuccess, handleValidationFailure, handlePaymentFinish, handleEnhancedFailure } = usePayFieldsErrors({
    config,
    paymentInfo,
    billingAddress,
    onSuccess,
    onFailure,
    setIsSubmitting,
    setValidationError,
    setScriptError,
  });

  usePayFieldsInitializer({
    config,
    loaded,
    payFieldsInitialized,
    billingAddress,
    setPayFieldsInitialized,
    setScriptError,
    setIsSubmitting,
    handlePaymentSuccess,
    handleEnhancedFailure,
    handleValidationFailure,
    handlePaymentFinish,
  });

  useEffect(() => {
    if (!config) {
      setScriptError("No payment configuration provided");
      return;
    }
  }, [config]);

  useEffect(() => {
    if (!config) return;

    // Only load script once - check if already loaded or loading
    if (loaded || window.PayFields) {
      setLoaded(true);
      return;
    }

    loadPayFieldsScript({
      onLoad: () => setLoaded(true),
      onError: () => {
        setScriptError("Failed to load payment processing script. Please try again later.");
        setIsSubmitting(false);
        toast.error("Failed to load payment processor");
      },
    }).catch((error) => {
      setScriptError(error.message);
    });

    // Don't cleanup script on unmount to allow reuse
  }, [config?.merchantId, loaded]); // Use stable dependencies to prevent re-runs

  const handleSubmit = useCallback(() => {
    if (!window.PayFields || isSubmitting || !config) return;

    setIsSubmitting(true);
    setValidationError(null);

    postMessageToParent("PAYMENT_SUBMISSION_STARTED");

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isSubmitting) {
        setIsSubmitting(false);
        const timeoutMessage = "Payment processing timed out. Please try again.";
        toast.error(timeoutMessage);
        postMessageToParent("PAYMENT_TIMEOUT", { error: timeoutMessage });
        if (onFailure) onFailure({ message: timeoutMessage });
      }
    }, 30000);

    try {
      window.PayFields.submit();
    } catch (error) {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      setIsSubmitting(false);
      const errorMessage = "Error processing payment. Please try again.";
      toast.error(errorMessage);
      postMessageToParent("PAYMENT_SUBMISSION_ERROR", {
        error: errorMessage,
        details: error,
      });
      if (onFailure) onFailure({ message: errorMessage });
    }
  }, [config, isSubmitting, onFailure]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    loaded,
    scriptError,
    isSubmitting,
    validationError,
    handleSubmit,
  };
};
