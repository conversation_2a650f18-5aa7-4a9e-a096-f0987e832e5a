import { useCallback } from "react";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import {
  createPaymentSuccessHandler,
  createPaymentFailureHandler,
  createValidationFailureHandler,
  createPaymentFinishHandler,
} from "../handlers/payment-handlers";

interface UsePayFieldsErrorsOptions {
  config: PayFieldsConfig;
  paymentInfo?: PaymentInfo | null;
  billingAddress?: BillingAddress;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
  setIsSubmitting: (value: boolean) => void;
  setValidationError: (error: string | null) => void;
  setScriptError: (error: string | null) => void;
}

export const usePayFieldsErrors = ({
  config,
  paymentInfo,
  billingAddress,
  onSuccess,
  onFailure,
  setIsSubmitting,
  setValidationError,
  setScriptError,
}: UsePayFieldsErrorsOptions) => {
  const handlePaymentSuccess = useCallback(
    async (response: unknown) => {
      setIsSubmitting(false);
      setValidationError(null);

      try {
        const handler = createPaymentSuccessHandler(config, paymentInfo || null, billingAddress, onSuccess);
        await handler(response as PaymentResponse);
      } catch (error) {
        setValidationError(error instanceof Error ? error.message : "Payment processing failed");
      }
    },
    [config, paymentInfo, billingAddress, onSuccess, setIsSubmitting, setValidationError]
  );

  const handlePaymentFailure = useCallback(
    (err: unknown) => {
      setIsSubmitting(false);
      const handler = createPaymentFailureHandler(onFailure);
      handler(err as PaymentError);
      setValidationError((err as PaymentError)?.message || "Payment processing failed");
    },
    [onFailure, setIsSubmitting, setValidationError]
  );

  const handleValidationFailure = useCallback(
    (err: unknown) => {
      setIsSubmitting(false);
      const handler = createValidationFailureHandler(onFailure);
      handler(err);
      setValidationError("Payment validation failed. Please check your card details.");
    },
    [onFailure, setIsSubmitting, setValidationError]
  );

  const handlePaymentFinish = useCallback(
    (response: unknown) => {
      setIsSubmitting(false);
      const handler = createPaymentFinishHandler();
      handler(response);
    },
    [setIsSubmitting]
  );

  const handleEnhancedFailure = useCallback(
    (err: unknown) => {
      const error = err as PaymentError;
      if (error && error.message) {
        if (error.message.includes("API key") || error.message.includes("apiKey")) {
          setScriptError("Invalid API key. Please check your payment configuration.");
        } else if (error.message.includes("merchant") || error.message.includes("Merchant")) {
          setScriptError("Invalid merchant ID. Please check your payment configuration.");
        } else if (error.message.includes("unauthorized") || error.message.includes("forbidden")) {
          setScriptError("Payment authorization failed. Check your credentials and environment.");
        } else {
          setScriptError(`Payment initialization failed: ${error.message}`);
        }
      } else {
        setScriptError("Payment initialization failed with unknown error.");
      }
      handlePaymentFailure(err);
    },
    [handlePaymentFailure, setScriptError]
  );

  return {
    handlePaymentSuccess,
    handlePaymentFailure,
    handleValidationFailure,
    handlePaymentFinish,
    handleEnhancedFailure,
  };
};
