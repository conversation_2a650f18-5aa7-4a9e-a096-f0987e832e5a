import { useCallback, useRef } from "react";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import {
  createPaymentSuccessHandler,
  createPaymentFailureHandler,
  createValidationFailureHandler,
  createPaymentFinishHandler,
} from "../handlers/payment-handlers";

interface UsePayFieldsErrorsOptions {
  config: PayFieldsConfig;
  paymentInfo?: PaymentInfo | null;
  billingAddress?: BillingAddress;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
  setIsSubmitting: (value: boolean) => void;
  setValidationError: (error: string | null) => void;
  setScriptError: (error: string | null) => void;
}

export const usePayFieldsErrors = ({
  config,
  paymentInfo,
  billingAddress,
  onSuccess,
  onFailure,
  setIsSubmitting,
  setValidationError,
  setScriptError,
}: UsePayFieldsErrorsOptions) => {
  // Use refs to store current values without causing re-renders
  const configRef = useRef(config);
  const paymentInfoRef = useRef(paymentInfo);
  const billingAddressRef = useRef(billingAddress);
  const onSuccessRef = useRef(onSuccess);
  const onFailureRef = useRef(onFailure);

  // Update refs when values change
  configRef.current = config;
  paymentInfoRef.current = paymentInfo;
  billingAddressRef.current = billingAddress;
  onSuccessRef.current = onSuccess;
  onFailureRef.current = onFailure;

  const handlePaymentSuccess = useCallback(
    async (response: unknown) => {
      setIsSubmitting(false);
      setValidationError(null);

      try {
        const handler = createPaymentSuccessHandler(
          configRef.current,
          paymentInfoRef.current || null,
          billingAddressRef.current,
          onSuccessRef.current
        );
        await handler(response as PaymentResponse);
      } catch (error) {
        setValidationError(error instanceof Error ? error.message : "Payment processing failed");
      }
    },
    [setIsSubmitting, setValidationError]
  );

  const handlePaymentFailure = useCallback(
    (err: unknown) => {
      setIsSubmitting(false);
      const handler = createPaymentFailureHandler(onFailureRef.current);
      handler(err as PaymentError);
      setValidationError((err as PaymentError)?.message || "Payment processing failed");
    },
    [setIsSubmitting, setValidationError]
  );

  const handleValidationFailure = useCallback(
    (err: unknown) => {
      setIsSubmitting(false);
      const handler = createValidationFailureHandler(onFailureRef.current);
      handler(err);
      setValidationError("Payment validation failed. Please check your card details.");
    },
    [setIsSubmitting, setValidationError]
  );

  const handlePaymentFinish = useCallback(
    (response: unknown) => {
      setIsSubmitting(false);
      const handler = createPaymentFinishHandler();
      handler(response);
    },
    [setIsSubmitting]
  );

  const handleEnhancedFailure = useCallback(
    (err: unknown) => {
      const error = err as PaymentError;
      if (error && error.message) {
        if (error.message.includes("API key") || error.message.includes("apiKey")) {
          setScriptError("Invalid API key. Please check your payment configuration.");
        } else if (error.message.includes("merchant") || error.message.includes("Merchant")) {
          setScriptError("Invalid merchant ID. Please check your payment configuration.");
        } else if (error.message.includes("unauthorized") || error.message.includes("forbidden")) {
          setScriptError("Payment authorization failed. Check your credentials and environment.");
        } else {
          setScriptError(`Payment initialization failed: ${error.message}`);
        }
      } else {
        setScriptError("Payment initialization failed with unknown error.");
      }
      handlePaymentFailure(err);
    },
    [handlePaymentFailure, setScriptError]
  );

  return {
    handlePaymentSuccess,
    handlePaymentFailure,
    handleValidationFailure,
    handlePaymentFinish,
    handleEnhancedFailure,
  };
};
