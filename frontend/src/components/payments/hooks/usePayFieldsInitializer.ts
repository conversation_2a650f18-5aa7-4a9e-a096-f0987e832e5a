import { useEffect, useRef } from "react";
import { toast } from "sonner";
import { PayFieldsConfig, BillingAddress } from "../types/payfields.types";
import { initializePayFields, updateBillingAddress, resetPayFields } from "../utils/payfields-initializer";

interface UsePayFieldsInitializerOptions {
  config: PayFieldsConfig;
  loaded: boolean;
  payFieldsInitialized: boolean;
  billingAddress?: BillingAddress;
  setPayFieldsInitialized: (value: boolean) => void;
  setScriptError: (error: string | null) => void;
  setIsSubmitting: (value: boolean) => void;
  handlePaymentSuccess: (response: unknown) => void;
  handleEnhancedFailure: (error: unknown) => void;
  handleValidationFailure: (error: unknown) => void;
  handlePaymentFinish: (response: unknown) => void;
}

export const usePayFieldsInitializer = ({
  config,
  loaded,
  payFieldsInitialized,
  billingAddress,
  setPayFieldsInitialized,
  setScriptError,
  setIsSubmitting,
  handlePaymentSuccess,
  handleEnhancedFailure,
  handleValidationFailure,
  handlePaymentFinish,
}: UsePayFieldsInitializerOptions) => {
  // Create a stable configuration key to detect meaningful changes
  const configKey = useRef<string>("");
  const currentConfigKey = `${config?.merchantId}-${config?.mode}-${config?.googlePayConfig?.enabled}`;

  // Reset PayFields when configuration meaningfully changes (payment method switch)
  useEffect(() => {
    if (configKey.current && configKey.current !== currentConfigKey && payFieldsInitialized && window.PayFields) {
      resetPayFields();
      setPayFieldsInitialized(false);
    }
    configKey.current = currentConfigKey;
  }, [currentConfigKey, payFieldsInitialized, setPayFieldsInitialized]);

  // Store handlers in refs to avoid dependency issues
  const handlersRef = useRef({
    handlePaymentSuccess,
    handleEnhancedFailure,
    handleValidationFailure,
    handlePaymentFinish,
  });

  // Update handlers ref when they change
  handlersRef.current = {
    handlePaymentSuccess,
    handleEnhancedFailure,
    handleValidationFailure,
    handlePaymentFinish,
  };

  useEffect(() => {
    if (!loaded || !window.PayFields || !config || payFieldsInitialized) return;

    const timeoutId = setTimeout(() => {
      try {
        initializePayFields(config, {
          onSuccess: handlersRef.current.handlePaymentSuccess,
          onFailure: handlersRef.current.handleEnhancedFailure,
          onValidationFailure: handlersRef.current.handleValidationFailure,
          onFinish: handlersRef.current.handlePaymentFinish,
          billingAddress,
        });

        setPayFieldsInitialized(true);
      } catch {
        setScriptError("Error initializing payment processor. Please try again later.");
        setIsSubmitting(false);
        toast.error("Payment configuration error");
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [loaded, payFieldsInitialized, currentConfigKey, billingAddress, setPayFieldsInitialized, setScriptError, setIsSubmitting]);

  useEffect(() => {
    if (!payFieldsInitialized || !window.PayFields) return;

    try {
      window.PayFields.onSuccess = handlersRef.current.handlePaymentSuccess;
      window.PayFields.onFailure = handlersRef.current.handleEnhancedFailure;
      window.PayFields.onValidationFailure = handlersRef.current.handleValidationFailure;
      window.PayFields.onFinish = handlersRef.current.handlePaymentFinish;
    } catch {
      // Silent error - callbacks will be set on next render
    }
  }, [payFieldsInitialized]);

  useEffect(() => {
    if (!window.PayFields || !billingAddress) return;
    updateBillingAddress(billingAddress);
  }, [billingAddress]);
};
