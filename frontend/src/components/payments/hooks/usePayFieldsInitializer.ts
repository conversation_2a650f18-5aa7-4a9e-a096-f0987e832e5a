import { useEffect } from "react";
import { toast } from "sonner";
import { PayFieldsConfig, BillingAddress } from "../types/payfields.types";
import { initializePayFields, updateBillingAddress } from "../utils/payfields-initializer";

interface UsePayFieldsInitializerOptions {
  config: PayFieldsConfig;
  loaded: boolean;
  payFieldsInitialized: boolean;
  billingAddress?: BillingAddress;
  setPayFieldsInitialized: (value: boolean) => void;
  setScriptError: (error: string | null) => void;
  setIsSubmitting: (value: boolean) => void;
  handlePaymentSuccess: (response: unknown) => void;
  handleEnhancedFailure: (error: unknown) => void;
  handleValidationFailure: (error: unknown) => void;
  handlePaymentFinish: (response: unknown) => void;
}

export const usePayFieldsInitializer = ({
  config,
  loaded,
  payFieldsInitialized,
  billingAddress,
  setPayFieldsInitialized,
  setScriptError,
  setIsSubmitting,
  handlePaymentSuccess,
  handleEnhancedFailure,
  handleValidationFailure,
  handlePaymentFinish,
}: UsePayFieldsInitializerOptions) => {
  useEffect(() => {
    if (!loaded || !window.PayFields || !config || payFieldsInitialized) return;


    setTimeout(() => {
      try {
        initializePayFields(config, {
          onSuccess: handlePaymentSuccess,
          onFailure: handleEnhancedFailure,
          onValidationFailure: handleValidationFailure,
          onFinish: handlePaymentFinish,
          billingAddress,
        });

        setPayFieldsInitialized(true);
      } catch {
        setScriptError("Error initializing payment processor. Please try again later.");
        setIsSubmitting(false);
        toast.error("Payment configuration error");
      }
    }, 500);
  }, [
    loaded,
    payFieldsInitialized,
    config,
    billingAddress,
    handlePaymentSuccess,
    handleEnhancedFailure,
    handleValidationFailure,
    handlePaymentFinish,
    setPayFieldsInitialized,
    setScriptError,
    setIsSubmitting,
  ]);

  useEffect(() => {
    if (!payFieldsInitialized || !window.PayFields) return;

    try {
      window.PayFields.onSuccess = handlePaymentSuccess;
      window.PayFields.onFailure = handleEnhancedFailure;
      window.PayFields.onValidationFailure = handleValidationFailure;
      window.PayFields.onFinish = handlePaymentFinish;
    } catch {
      // Silent error - callbacks will be set on next render
    }
  }, [payFieldsInitialized, handlePaymentSuccess, handleEnhancedFailure, handleValidationFailure, handlePaymentFinish]);

  useEffect(() => {
    if (!window.PayFields || !billingAddress) return;
    updateBillingAddress(billingAddress);
  }, [billingAddress]);
};
