import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { prevStep, goToStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import {
  ReviewBusinessInfoSection,
  ReviewOwnerInfoSection,
  ReviewBankAccountSection,
  ComplianceCheckboxesSection,
  ImportantNoticeSection,
  SubmissionErrorSection,
  SubmissionSuccessPage,
  SubmissionLoadingOverlay,
  IpDetectionSection,
  ReviewNavigationButtons,
  ReviewPageLayout,
} from "./sections";
import { useClientIp } from "./hooks/useClientIp";
import { useSubmitOnboarding } from "./hooks/useSubmitOnboarding";
import { useComplianceState } from "./hooks/useComplianceState";
import { type ComplianceValidationErrors } from "./utils/reviewValidation";

const OnboardingReview = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const ipDetection = useClientIp();

  const [validationErrors, setValidationErrors] = useState<ComplianceValidationErrors>({});

  const { complianceState, handleComplianceChange } = useComplianceState({
    validationErrors,
    setValidationErrors,
  });

  const { isSubmitting, submitError, submitSuccess, submitResult, handleSubmit } = useSubmitOnboarding({
    formData,
    complianceState,
    clientIp: ipDetection.ip,
  });

  if (submitSuccess) {
    return <SubmissionSuccessPage submitResult={submitResult} />;
  }

  return (
    <ReviewPageLayout title="Review & Submit" subtitle="Please review all information before submitting your application">
      <ReviewBusinessInfoSection formData={formData} onEdit={() => dispatch(goToStep(1))} />

      <ReviewOwnerInfoSection formData={formData} onEdit={() => dispatch(goToStep(2))} />

      <ReviewBankAccountSection formData={formData} onEdit={() => dispatch(goToStep(3))} />

      <IpDetectionSection ipDetection={ipDetection} />

      <ComplianceCheckboxesSection complianceState={complianceState} validationErrors={validationErrors} onChange={handleComplianceChange} />

      <ImportantNoticeSection />

      {submitError && <SubmissionErrorSection error={submitError} />}

      <ReviewNavigationButtons
        isSubmitting={isSubmitting}
        ipDetection={ipDetection}
        onPrevious={() => dispatch(prevStep())}
        onSubmit={handleSubmit}
      />

      {isSubmitting && <SubmissionLoadingOverlay />}
    </ReviewPageLayout>
  );
};

export default OnboardingReview;
