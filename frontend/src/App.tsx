import { Suspense, lazy } from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";

import LoadingSpinner from "./components/LoadingSpinner.tsx";
import { AppLayout } from "./components/layouts/AppLayout";
import { Toaster } from "./ui/sonner.tsx";
import ErrorsRouting from "./components/routing/ErrorsRouting.tsx";

const Onboarding = lazy(() => import("./pages/OnboardingPage.tsx"));
const OnboardingSuccess = lazy(() => import("./pages/OnboardingSuccess.tsx"));
const Home = lazy(() => import("./pages/Home.tsx"));
const PaymentPage = lazy(() => import("./pages/PaymentPage.tsx"));
const PaymentIframe = lazy(() => import("./pages/PaymentIframe.tsx"));
const IframeIntegrationDocs = lazy(() => import("./pages/IframeIntegrationDocs.tsx"));
const IframeDemoPage = lazy(() => import("./pages/IframeDemoPage.tsx"));

function App() {
  return (
    <Router>
      <Toaster />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          {/* Iframe Route - No navigation or padding */}
          <Route path="/payment-iframe" element={<PaymentIframe />} />

          <Route element={<AppLayout />}>
            <Route path="/" element={<Home />} />

            {/* Onboarding Routes */}
            <Route path="/onboarding" element={<Onboarding />} />
            <Route path="/onboarding-success" element={<OnboardingSuccess />} />

            {/* Payment Routes */}
            <Route path="/payment" element={<PaymentPage />} />
            <Route path="/iframe-integration" element={<IframeIntegrationDocs />} />
            <Route path="/iframe-demo" element={<IframeDemoPage />} />

            {/* Error Routes */}
            <Route path="/error/*" element={<ErrorsRouting />} />

            {/* Fallback for unknown routes - redirect to 404 */}
            <Route path="*" element={<ErrorsRouting />} />
          </Route>
        </Routes>
      </Suspense>
    </Router>
  );
}

export default App;
