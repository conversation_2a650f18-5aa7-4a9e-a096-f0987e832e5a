export interface PaymentConfigRequest {
  merchantId: string;
  description: string;
  amount?: number;
  mode?: "txn" | "txnToken" | "token";
}

export interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
}

export interface PaymentConfigResponse {
  config: PayFieldsConfig;
  message: string;
  merchantInfo: {
    id: string;
    name: string;
    status: number;
  };
}

export interface GooglePayConfig {
  enabled: boolean;
  merchantName: string;
  environment: "TEST" | "PRODUCTION";
  allowedCardNetworks: string[];
  allowedCardAuthMethods: string[];
  billingAddressRequired: boolean;
  phoneNumberRequired: boolean;
}

export interface GenerateIntegrationTokenRequest {
  merchantId: string;
  description: string;
  amount?: number;
  returnUrl?: string;
  expiresIn?: number;
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  googlePayConfig?: GooglePayConfig;
  enableDigitalWallets?: boolean;
}

export interface GenerateIntegrationTokenResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    expiresAt: string;
    embedUrl: string;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
  };
}

export interface ValidateIframeTokenResponse {
  success: boolean;
  message: string;
  data?: {
    config: PayFieldsConfig;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
    paymentInfo: {
      description: string;
      amount: number;
      returnUrl?: string;
    };
  };
}

export interface TokenStatusResponse {
  success: boolean;
  message: string;
  data: {
    isValid: boolean;
    status: "valid" | "expired" | "used" | "invalid";
    expiresAt?: string;
    timeRemaining?: number;
    merchantId?: string;
    amount?: number;
    description?: string;
  };
}

export interface TokenPaymentRequest {
  merchantId: string;
  token: string;
  tokenId?: string; // The tokenId (data[0].id) for deletion - format: t1_tok_*
  amount: number;
  description?: string;
  paymentType?: "CARD" | "GOOGLE_PAY" | "APPLE_PAY"; // Type of payment token
  customerInfo?: {
    email?: string;
    name?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
  };
}

export interface TokenPaymentResponse {
  success: boolean;
  message: string;
  transaction?: {
    id: string;
    status: string;
    amount: number;
    merchantId: string;
    description: string;
    createdAt: string;
  };
  merchantInfo?: {
    id: string;
    name: string;
    status: number;
  };
  error?: string;
}
