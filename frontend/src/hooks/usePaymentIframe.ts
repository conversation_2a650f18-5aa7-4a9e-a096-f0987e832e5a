import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { validateIframeToken } from "../services/api";
import type { PayFieldsConfig, MerchantInfo, PaymentInfo } from "../types/payment";
import { configureIframeBodyStyles, configureIframeViewport, formatErrorMessage } from "../utils/paymentUtils";

interface UsePaymentIframeReturn {
  payFieldsConfig: PayFieldsConfig | null;
  merchantInfo: MerchantInfo | null;
  paymentInfo: PaymentInfo | null;
  error: string | null;
  loading: boolean;
  selectedPaymentMethod: "card" | "google_pay" | null;
  setSelectedPaymentMethod: (method: "card" | "google_pay") => void;
}

export const usePaymentIframe = (): UsePaymentIframeReturn => {
  const [searchParams] = useSearchParams();
  const [payFieldsConfig, setPayFieldsConfig] = useState<PayFieldsConfig | null>(null);
  const [merchantInfo, setMerchantInfo] = useState<MerchantInfo | null>(null);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedPaymentMethod, setSelectedPaymentMethodState] = useState<"card" | "google_pay" | null>(null);
  const [token, setToken] = useState<string | null>(null);

  // Handle payment method selection
  const setSelectedPaymentMethod = async (method: "card" | "google_pay") => {
    if (!token) return;

    // Clear previous configuration to force re-initialization
    setPayFieldsConfig(null);
    setSelectedPaymentMethodState(method);
    setLoading(true);
    setError(null);

    try {
      const data = await validateIframeToken(token, method);

      if (!data.success || !data.data) {
        throw new Error(data.message || "Invalid token or configuration");
      }

      setPayFieldsConfig(data.data.config);
      setMerchantInfo(data.data.merchantInfo);
      setPaymentInfo(data.data.paymentInfo);
    } catch (error) {
      console.error("Failed to validate token with payment method:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load payment configuration";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Configure iframe-specific styling
    configureIframeBodyStyles();
    configureIframeViewport();

    // Get token from URL parameters
    const tokenParam = searchParams.get("token");

    if (!tokenParam) {
      setError("Missing payment token. Please use a valid payment link.");
      setLoading(false);
      return;
    }

    setToken(tokenParam);

    // Initial validation without payment method (will show selector)
    const validateTokenAndLoadConfig = async () => {
      try {
        // Default to card for initial load
        const data = await validateIframeToken(tokenParam, "card");

        if (!data.success || !data.data) {
          throw new Error(data.message || "Invalid token or configuration");
        }

        setPayFieldsConfig(data.data.config);
        setMerchantInfo(data.data.merchantInfo);
        setPaymentInfo(data.data.paymentInfo);
        setLoading(false);

        // Notify parent window that iframe is ready
        if (window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_IFRAME_READY",
              data: {
                merchantName: data.data.merchantInfo.name,
                amount: data.data.paymentInfo.amount,
                description: data.data.paymentInfo.description,
              },
            },
            "*"
          );
        }
      } catch (error) {
        console.error("Failed to validate token and load payment configuration:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load payment configuration";
        setError(errorMessage);
        setLoading(false);

        // Notify parent window of error
        if (window.parent !== window) {
          window.parent.postMessage(formatErrorMessage(errorMessage, "PAYMENT_IFRAME_ERROR"), "*");
        }
      }
    };

    validateTokenAndLoadConfig();
  }, [searchParams]);

  return {
    payFieldsConfig,
    merchantInfo,
    paymentInfo,
    error,
    loading,
    selectedPaymentMethod,
    setSelectedPaymentMethod,
  };
};
